-- Inserts para los servicios monitoreados
-- Basado en la configuración actual de MONITORED_SERVICES

-- Limpiar datos existentes (opcional, comentar si no quieres borrar datos)
-- DELETE FROM monitored_services;

-- Insertar los servicios actuales
INSERT INTO monitored_services (name, container_name, url, icon, description, is_active) VALUES
('Traefik', 'traefik', 'https://traefik.msarknet.me', '⚙️', 'Panel de control del proxy reverso', TRUE),
('Adminer', 'adminer', 'https://adminer.msarknet.me', '🗄️', 'Administrador de base de datos', TRUE),
('MariaDB', 'mariadb', NULL, '🗃️', 'Base de datos MariaDB', TRUE),
('Cerebro Frontend', 'cerebro-fe', 'https://msarknet.me', '🧠', 'Frontend React de Cerebro', TRUE),
('Cerebro Backend', 'cerebro-be', 'https://api.msarknet.me', '⚡', 'Backend Node.js de Cerebro', TRUE)
('Webview Health Monitor', 'health-fe', 'https://status.msarknet.me', '🏥', 'Webview monitoreo de servicios', TRUE),
('API Health Monitor', 'health-be', 'https://api-status.msarknet.me', '⚡', 'API de monitoreo de servicios', TRUE),
('Portainer', 'portainer', 'https://portainer.msarknet.me', '⚙️', 'Panel de control de Docker', FALSE),
('Whoami', 'whoami', 'https://whoami.msarknet.me', '⚙️', 'Servicio de testing whoami', FALSE),
('Grafana', 'grafana', 'https://grafana.msarknet.me', '📊', 'Panel de monitorización', FALSE),
('Prometheus', 'prometheus', 'https://prom.msarknet.me', '📈', 'Servicio de métricas', FALSE),
('Mkdocs', 'mkdocs', 'https://docs.msarknet.me', '📚', 'Documentación del proyecto', TRUE);

-- Verificar los datos insertados
SELECT
    id,
    name,
    container_name,
    url,
    icon,
    description,
    is_active,
    created_at
FROM monitored_services
ORDER BY id;
