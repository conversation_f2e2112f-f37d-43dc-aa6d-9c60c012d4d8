// Cargar variables de entorno
require('dotenv').config();

const express = require('express');
const Docker = require('dockerode');
const { testConnection } = require('./config/database');
const MonitoredService = require('./models/MonitoredService');

const app = express();
const port = process.env.PORT || 5001;

// Configurar Docker
const docker = new Docker({ socketPath: '/var/run/docker.sock' });

// Función para obtener el estado de un contenedor
async function getContainerStatus(containerName) {
  try {
    const containers = await docker.listContainers({ all: true });
    const container = containers.find(c =>
      c.Names.some(name => name.includes(containerName))
    );

    if (!container) {
      return {
        status: 'not_found',
        state: 'Container not found',
        uptime: null,
        health: 'unknown'
      };
    }

    // Obtener información detallada del contenedor
    const containerInfo = docker.getContainer(container.Id);
    const inspect = await containerInfo.inspect();

    return {
      status: container.State === 'running' ? 'running' : container.State,
      state: container.State,
      uptime: container.Status,
      health: inspect.State.Health ? inspect.State.Health.Status : 'no-healthcheck',
      created: container.Created,
      image: container.Image,
      ports: container.Ports || []
    };
  } catch (error) {
    console.error(`Error getting status for ${containerName}:`, error.message);
    return {
      status: 'error',
      state: 'Error checking container',
      uptime: null,
      health: 'unknown',
      error: error.message
    };
  }
}

// Endpoint raíz - Todo está bien
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'API Health is running',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// Endpoint para obtener el estado de todos los servicios
app.get('/api/services/status', async (req, res) => {
  try {
    // Obtener servicios desde la base de datos
    const services = await MonitoredService.getAll();

    const servicesStatus = await Promise.all(
      services.map(async (service) => {
        const containerStatus = await getContainerStatus(service.container_name);

        return {
          id: service.id,
          name: service.name,
          containerName: service.container_name,
          url: service.url,
          icon: service.icon,
          description: service.description,
          ...containerStatus,
          lastChecked: new Date().toISOString()
        };
      })
    );

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      services: servicesStatus
    });
  } catch (error) {
    console.error('Error getting services status:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para obtener el estado de un servicio específico
app.get('/api/services/:serviceName/status', async (req, res) => {
  try {
    const { serviceName } = req.params;

    // Buscar el servicio en la base de datos
    const service = await MonitoredService.getByName(serviceName);

    if (!service) {
      return res.status(404).json({
        success: false,
        error: 'Service not found'
      });
    }

    const containerStatus = await getContainerStatus(service.container_name);

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      service: {
        id: service.id,
        name: service.name,
        containerName: service.container_name,
        url: service.url,
        icon: service.icon,
        description: service.description,
        ...containerStatus,
        lastChecked: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(`Error getting status for service ${req.params.serviceName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint de health check
app.get('/api/health', async (req, res) => {
  try {
    const dbConnected = await testConnection();
    const serviceCount = dbConnected ? await MonitoredService.count() : 0;

    res.json({
      success: true,
      message: 'Health API is running',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: {
        connected: dbConnected,
        services_count: serviceCount
      }
    });
  } catch (error) {
    res.json({
      success: true,
      message: 'Health API is running',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: {
        connected: false,
        error: error.message
      }
    });
  }
});

// Endpoint para listar todos los servicios (sin estado de contenedor)
app.get('/api/services', async (req, res) => {
  try {
    const services = await MonitoredService.getAll();

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      count: services.length,
      services: services.map(service => ({
        id: service.id,
        name: service.name,
        containerName: service.container_name,
        url: service.url,
        icon: service.icon,
        description: service.description,
        isActive: service.is_active,
        createdAt: service.created_at,
        updatedAt: service.updated_at
      }))
    });
  } catch (error) {
    console.error('Error getting services list:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para crear un nuevo servicio
app.post('/api/services', async (req, res) => {
  try {
    const { name, containerName, url, icon, description, isActive } = req.body;

    // Validaciones básicas
    if (!name || !containerName || !description) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, containerName, description'
      });
    }

    const serviceId = await MonitoredService.create({
      name,
      containerName,
      url: url || null,
      icon: icon || '🔧',
      description,
      isActive: isActive !== undefined ? isActive : true
    });

    res.status(201).json({
      success: true,
      message: 'Service created successfully',
      serviceId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error creating service:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para obtener información general de Docker
app.get('/api/docker/info', async (req, res) => {
  try {
    const info = await docker.info();
    const version = await docker.version();

    res.json({
      success: true,
      docker: {
        version: version.Version,
        apiVersion: version.ApiVersion,
        containers: info.Containers,
        containersRunning: info.ContainersRunning,
        containersPaused: info.ContainersPaused,
        containersStopped: info.ContainersStopped,
        images: info.Images
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting Docker info:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Función para inicializar la aplicación
async function initializeApp() {
  try {
    // Probar conexión a la base de datos
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.warn('⚠️  Database connection failed, but server will start anyway');
    }

    // Iniciar servidor
    app.listen(port, '0.0.0.0', () => {
      console.log(`🚀 Health API running on port ${port}`);
      console.log(`📊 API endpoints available at:`);
      console.log(`   - GET  /api/health`);
      console.log(`   - GET  /api/services/status`);
      console.log(`   - GET  /api/services/:serviceName/status`);
      console.log(`   - GET  /api/services (list all services)`);
      console.log(`   - POST /api/services (create new service)`);
      console.log(`   - GET  /api/docker/info`);
    });
  } catch (error) {
    console.error('❌ Failed to initialize application:', error.message);
    process.exit(1);
  }
}

// Inicializar la aplicación
initializeApp();
